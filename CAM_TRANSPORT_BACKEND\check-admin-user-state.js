const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function checkAdminUserState() {
    try {
        // Connect to the database using the same config as the main app
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');
        
        // Find the admin user
        const user = await Login.findOne({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });
        
        if (!user) {
            console.log('❌ Admin user not found');
            return;
        }
        
        console.log('👤 Admin User State:');
        console.log('=' .repeat(50));
        console.log(`ID: ${user._id}`);
        console.log(`Username: ${user.username}`);
        console.log(`Email: ${user.email}`);
        console.log(`isVerified: ${user.isVerified}`);
        console.log(`mfaEnabled: ${user.mfaEnabled}`);
        console.log(`isActive: ${user.isActive}`);
        console.log(`Role: ${user.role}`);
        console.log(`Last Login: ${user.lastLoginDate}`);
        console.log(`Created: ${user.createdAt}`);
        console.log(`Updated: ${user.updatedAt}`);
        
        if (user.mfaSecret) {
            console.log(`MFA Secret: ${user.mfaSecret}`);
            console.log(`MFA Secret Length: ${user.mfaSecret.length}`);
        } else {
            console.log('MFA Secret: null');
        }
        
        if (user.mfaDevices && user.mfaDevices.length > 0) {
            console.log(`MFA Devices: ${user.mfaDevices.length}`);
            user.mfaDevices.forEach((device, index) => {
                console.log(`  Device ${index + 1}:`);
                console.log(`    ID: ${device.deviceId}`);
                console.log(`    Name: ${device.deviceName}`);
                console.log(`    Active: ${device.isActive}`);
                console.log(`    Registered: ${device.registeredAt}`);
            });
        } else {
            console.log('MFA Devices: 0');
        }
        
        if (user.backupCodes && user.backupCodes.length > 0) {
            console.log(`Backup Codes: ${user.backupCodes.length}`);
            user.backupCodes.forEach((code, index) => {
                console.log(`  Code ${index + 1}: ${code.code} (Used: ${code.used})`);
            });
        } else {
            console.log('Backup Codes: 0');
        }
        
        console.log('\n🔍 Expected Behavior Analysis:');
        console.log('=' .repeat(50));
        
        if (user.mfaEnabled) {
            console.log('🔐 User has mfaEnabled=true');
            console.log('🔐 Should require TOTP verification');
            
            if (user.mfaSecret) {
                console.log('✅ MFA Secret is present');
                
                // Test token generation
                const speakeasy = require('speakeasy');
                const currentToken = speakeasy.totp({
                    secret: user.mfaSecret,
                    encoding: 'base32'
                });
                
                console.log(`🔐 Current expected TOTP: ${currentToken}`);
                console.log('📱 Use this token to test login');
                
                // Test verification
                const verified = speakeasy.totp.verify({
                    secret: user.mfaSecret,
                    encoding: 'base32',
                    token: currentToken,
                    window: 1
                });
                
                console.log(`✅ Token verification test: ${verified ? 'PASS' : 'FAIL'}`);
            } else {
                console.log('❌ MFA Secret is missing');
            }
            
            if (user.mfaDevices && user.mfaDevices.length > 0) {
                const activeDevices = user.mfaDevices.filter(d => d.isActive);
                console.log(`✅ Active MFA devices: ${activeDevices.length}`);
            } else {
                console.log('❌ No MFA devices found');
            }
        } else {
            console.log('✅ User has mfaEnabled=false');
            console.log('✅ Should require email OTP verification');
        }
        
        if (!user.isActive) {
            console.log('⚠️ User is INACTIVE - login should be blocked');
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('✅ Database connection closed');
    }
}

if (require.main === module) {
    checkAdminUserState();
}
