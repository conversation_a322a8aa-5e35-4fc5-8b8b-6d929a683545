
const Login = require("../model/Login");
let PasswordBcrypt = require("bcrypt");
const { sendEmail } = require("../service/SimpleMailer");



const LoginAdmin = async (req, res) => {

    const { username, email, password, mfaToken, step } = req.body;

    console.log('🚀 LoginAdmin called with:', { username, email, step, hasMfaToken: !!mfaToken });

    try {
        // Handle MFA verification step
        if (step === 'mfa' && mfaToken) {
            console.log('🔐 MFA verification step detected');

            // Find user by username or email
            let user = await Login.findOne({
                $or: [
                    { username: username },
                    { email: email },
                    { adminId: '1' } // Fallback to admin user
                ]
            });

            if (!user) {
                console.log('❌ User not found for MFA verification');
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            console.log('👤 User found for MFA verification:', {
                id: user._id,
                username: user.username,
                email: user.email,
                mfaEnabled: user.mfaEnabled
            });

            // Verify MFA token
            if (!user.mfaEnabled) {
                console.log('❌ MFA not enabled for user');
                return res.status(400).json({
                    success: false,
                    message: 'MFA is not enabled for this account'
                });
            }

            // Check if user has active MFA devices
            const activeDevices = user.mfaDevices?.filter(d => d.isActive) || [];
            if (activeDevices.length === 0) {
                console.log('❌ No active MFA devices found');
                return res.status(400).json({
                    success: false,
                    message: 'No active MFA devices found'
                });
            }

            // Verify the MFA token using master secret
            const speakeasy = require('speakeasy');
            let verified = false;

            console.log('🔍 Verifying MFA token using master secret');
            console.log('🔑 Token to verify:', mfaToken, 'Length:', mfaToken ? mfaToken.length : 0);

            // Check if user has master secret
            if (!user.mfaSecret) {
                console.log('❌ No master MFA secret found for user');
                return res.status(400).json({
                    success: false,
                    message: 'MFA secret not found. Please set up MFA again.'
                });
            }

            console.log('🔐 Using master secret:', {
                secretLength: user.mfaSecret ? user.mfaSecret.length : 0,
                secretPreview: user.mfaSecret ? `${user.mfaSecret.substring(0, 4)}****` : 'null'
            });

            // Generate current expected token for comparison
            const expectedToken = speakeasy.totp({
                secret: user.mfaSecret,
                encoding: 'base32'
            });

            verified = speakeasy.totp.verify({
                secret: user.mfaSecret,
                encoding: 'base32',
                token: mfaToken,
                window: 1 // Allow 1 window tolerance for better UX across different devices
            });

            console.log('🔍 Master secret verification result:', {
                verified: verified,
                providedToken: mfaToken ? `${mfaToken.substring(0, 2)}****` : 'null',
                expectedToken: expectedToken ? `${expectedToken.substring(0, 2)}****` : 'null',
                tokensMatch: mfaToken === expectedToken,
                window: 1
            });

            if (verified) {
                console.log('✅ Token verified successfully with master secret');

                // Update the most recently used device (best guess)
                if (activeDevices.length > 0) {
                    activeDevices[0].lastUsedAt = new Date();
                }
            }

            if (!verified) {
                console.log('❌ MFA token verification failed with master secret');
                console.log('🔍 Debug info:');
                console.log('  - Token provided:', mfaToken);
                console.log('  - Token length:', mfaToken ? mfaToken.length : 0);
                console.log('  - Master secret length:', user.mfaSecret ? user.mfaSecret.length : 0);

                // Generate current token for debugging
                const currentToken = speakeasy.totp({
                    secret: user.mfaSecret,
                    encoding: 'base32'
                });
                console.log('  - Expected current token:', currentToken);

                return res.status(400).json({
                    success: false,
                    message: 'Invalid MFA token'
                });
            }

            console.log('✅ MFA verification successful');

            // Update last MFA used
            user.lastMfaUsed = new Date();
            user.lastLoginDate = new Date();
            await user.save();

            return res.status(200).json({
                success: true,
                message: 'MFA verification successful',
                user: {
                    id: user._id,
                    _id: user._id,
                    username: user.username,
                    email: user.email,
                    isVerified: user.isVerified,
                    mfaVerified: true
                }
            });
        }

        // Handle regular login (first step)
        console.log('🔑 Regular login step');

        // Find existing user
        let user = await Login.findOne({
            $or: [
                { username: username },
                { email: email }
            ]
        });

        if (!user) {
            console.log('❌ User not found');
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        console.log('👤 User found, verifying password');

        // Verify password
        let isPasswordValid = false;

        if (user.username === 'admin' && password === 'admin') {
            // Allow plain text admin password for testing
            isPasswordValid = true;
            console.log('✅ Admin password verified (plain text)');
        } else {
            // Verify hashed password for all other users
            isPasswordValid = await PasswordBcrypt.compare(password, user.password);
            console.log('🔍 Password verification result:', isPasswordValid);
        }

        if (!isPasswordValid) {
            console.log('❌ Invalid password for user:', user.email);
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Check if user is active
        if (!user.isActive) {
            console.log('❌ User account is deactivated:', user.email);
            return res.status(403).json({
                success: false,
                message: 'Account is deactivated. Please contact administrator.'
            });
        }
        // Auto-setup MFA for admin user OR dhruv user if not already enabled
        if ((username === 'admin' || email === '<EMAIL>' || username === 'dhruv' || email === '<EMAIL>') && !user.mfaEnabled) {
            console.log('🔧 Auto-setting up MFA for admin user...');

            const crypto = require('crypto');

            // Use a FIXED secret for testing so it doesn't change
            const fixedSecret = 'JBSWY3DPEHPK3PXP'; // This is a well-known test secret

            // Set the master secret
            user.mfaSecret = fixedSecret;

            // Create an active MFA device (for tracking only, no individual secret)
            const deviceId = crypto.randomBytes(16).toString('hex');
            const mfaDevice = {
                deviceId,
                deviceName: 'Admin Test Authenticator',
                isActive: true,
                registeredAt: new Date(),
                lastUsedAt: new Date(),
                deviceInfo: {
                    userAgent: req.headers['user-agent'] || 'Admin Device',
                    ipAddress: req.ip || '127.0.0.1',
                    platform: 'Admin'
                }
            };

            // Generate backup codes
            const backupCodes = [];
            for (let i = 0; i < 10; i++) {
                backupCodes.push({
                    code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                    used: false,
                    usedAt: null
                });
            }

            // Enable MFA
            user.mfaEnabled = true;
            user.mfaSetupAt = new Date();
            user.mfaDevices = [mfaDevice];
            user.backupCodes = backupCodes;

            console.log('✅ MFA auto-setup completed for admin user');
            console.log('🔑 FIXED Master Secret for authenticator app:', fixedSecret);
            console.log('🔑 Add this secret to your authenticator: JBSWY3DPEHPK3PXP');
            console.log('📱 This same secret can be used on multiple devices!');
        }

        // CONDITIONAL AUTHENTICATION FLOW
        console.log('🔍 Checking authentication requirements for user:', {
            email: user.email,
            isVerified: user.isVerified,
            mfaEnabled: user.mfaEnabled
        });

        // If user is not verified, send email OTP for verification
        if (!user.isVerified) {
            console.log('📧 User not verified, sending email OTP...');

            const otp = Math.floor(100000 + Math.random() * 900000); // 6-digit OTP
            const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // OTP valid for 10 minutes

            // Save OTP and expiry to user
            user.otp = otp;
            user.otpExpiry = otpExpiry;

            // Send verification email (handle email failures gracefully)
            try {
                await sendEmail({
                    to: email,
                    subject: 'CAM Transport - Email Verification Required',
                    template: 'login_otp.ejs',
                    templateData: {
                        otp: otp,
                        userEmail: email,
                        verificationLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/en/email-verification?userId=${user._id}&otp=${otp}`,
                        userId: user._id,
                        username: user.username || 'User'
                    },
                    importance: 'high'
                });
                console.log('✅ Verification email sent successfully');
            } catch (emailError) {
                console.error('❌ Failed to send verification email:', emailError.message);
                // Continue with login process even if email fails
                console.log('⚠️ Continuing login process without email verification');
            }

            user.lastLoginDate = new Date();
            await user.save();

            return res.status(200).json({
                success: true,
                message: "Email verification required. Please check your email for the OTP.",
                requiresEmailVerification: true,
                user: {
                    id: user._id,
                    _id: user._id,
                    username: user.username,
                    email: user.email,
                    isVerified: user.isVerified
                }
            });
        }

        // MANDATORY EMAIL OTP VERIFICATION: If user doesn't have MFA enabled, ALWAYS require email OTP
        // This ensures that newly created users (mfaEnabled: false) cannot bypass verification
        if (!user.mfaEnabled) {
            console.log('🔒 MANDATORY EMAIL OTP: User does not have MFA enabled, requiring email OTP verification...');
            console.log(`📊 User status: isVerified=${user.isVerified}, mfaEnabled=${user.mfaEnabled}`);

            // CRITICAL: Clear any existing OTP first to invalidate previous ones
            console.log('🗑️ Clearing any existing OTP before generating new one...');
            user.otp = null;
            user.otpExpiry = null;
            await user.save(); // Save to ensure previous OTP is cleared

            // Generate new OTP
            const otp = Math.floor(100000 + Math.random() * 900000); // 6-digit OTP
            const otpExpiry = new Date(Date.now() + 5 * 60 * 1000); // OTP valid for 5 minutes only

            console.log(`🔐 Generated new OTP: ${otp} (expires in 5 minutes)`);

            // Save new OTP and expiry to user
            user.otp = otp;
            user.otpExpiry = otpExpiry;

            // Send login OTP email (handle email failures gracefully)
            try {
                await sendEmail({
                    to: email,
                    subject: 'CAM Transport - Login Verification Code',
                    template: 'login_otp.ejs',
                    templateData: {
                        otp: otp,
                        userEmail: email,
                        verificationLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/en/email-verification?userId=${user._id}&otp=${otp}`,
                        userId: user._id,
                        username: user.username || 'User'
                    },
                    importance: 'high'
                });
                console.log('✅ Login OTP email sent successfully');
            } catch (emailError) {
                console.error('❌ Failed to send login OTP email:', emailError.message);
                // SECURITY: Do NOT continue login process if email fails for mandatory verification
                console.log('🚫 BLOCKING login process - email OTP is mandatory for users without MFA');
                return res.status(500).json({
                    success: false,
                    message: 'Failed to send verification email. Please try again or contact support.',
                    requiresEmailOTP: true
                });
            }

            user.lastLoginDate = new Date();
            await user.save();

            return res.status(200).json({
                success: true,
                message: "Email OTP verification required. Please check your email for the verification code.",
                requiresEmailOTP: true,
                user: {
                    id: user._id,
                    _id: user._id,
                    username: user.username,
                    email: user.email,
                    isVerified: user.isVerified,
                    mfaEnabled: user.mfaEnabled
                }
            });
        }

        // FORCE DISABLE <NAME_EMAIL> to test Email OTP flow
        if (user.email === '<EMAIL>') {
            console.log('🔧 FORCE DISABLING <NAME_EMAIL> to test Email OTP');
            user.mfaEnabled = false;
            user.mfaSecret = null;
            user.mfaDevices = [];
            user.backupCodes = [];
            await user.save();
            console.log('✅ MFA disabled - user will now use Email OTP flow');
        }

        // If user has MFA enabled, require TOTP verification
        if (user.mfaEnabled) {
            console.log('🔐 User has MFA enabled, requiring TOTP verification...');

            user.lastLoginDate = new Date();
            await user.save();

            return res.status(200).json({
                success: true,
                message: "MFA verification required",
                requiresMFA: true,
                user: {
                    id: user._id,
                    _id: user._id,
                    username: user.username,
                    email: user.email,
                    isVerified: user.isVerified,
                    mfaEnabled: user.mfaEnabled
                }
            });
        }

        // SECURITY: This point should never be reached
        // All users must either have MFA enabled (handled above) or go through email OTP verification
        console.error('🚨 SECURITY VIOLATION: Reached end of login function without proper verification');
        console.error('🚨 User state:', {
            id: user._id,
            email: user.email,
            mfaEnabled: user.mfaEnabled,
            isVerified: user.isVerified
        });

        return res.status(500).json({
            success: false,
            message: "Authentication flow error. Please try again or contact support."
        });

    } catch (error) {
        console.error("Error creating user:", error);
        return res.status(500).json({ message: "Internal server error" });
    }

}

const VerifyOTP = async (req, res) => {
    const { userId, otp } = req.params;

    try {
        console.log(`� VerifyOTP function called!`);
        console.log(`�🔍 OTP Verification attempt - UserID: ${userId}, OTP: ${otp}`);

        // Find the user by ID
        const user = await Login.findById(userId);
        console.log(`🔍 User found:`, user ? `Email: ${user.email}, Current isVerified: ${user.isVerified}, Stored OTP: ${user.otp}` : 'No user found');

        if (!user) {
            console.log(`❌ User not found for ID: ${userId}`);
            return res.status(404).send(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Verification Failed</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }
                        .container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
                        .error { color: #dc3545; }
                        .icon { font-size: 48px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="icon">❌</div>
                        <h2 class="error">Verification Failed</h2>
                        <p>User not found. Please try again or contact support.</p>
                    </div>
                </body>
                </html>
            `);
        }

        console.log(`🔍 Comparing OTPs - Stored: ${user.otp}, Provided: ${otp}, Parsed: ${parseInt(otp)}`);

        // Check if OTP matches (convert URL parameter to number for comparison)
        if (user.otp !== parseInt(otp)) {
            console.log(`❌ OTP mismatch - Expected: ${user.otp}, Got: ${parseInt(otp)}`);
            return res.status(400).send(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Invalid OTP</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }
                        .container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
                        .error { color: #dc3545; }
                        .icon { font-size: 48px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="icon">🔒</div>
                        <h2 class="error">Invalid OTP</h2>
                        <p>The verification code is incorrect. Please check your email for the correct code.</p>
                    </div>
                </body>
                </html>
            `);
        }

        // Check if OTP has expired
        if (user.otpExpiry && new Date() > user.otpExpiry) {
            console.log(`⏰ OTP expired - Expiry: ${user.otpExpiry}, Current: ${new Date()}`);
            return res.status(400).send(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OTP Expired</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }
                        .container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
                        .error { color: #dc3545; }
                        .icon { font-size: 48px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="icon">⏰</div>
                        <h2 class="error">OTP Expired</h2>
                        <p>The verification code has expired. Please request a new login to get a fresh code.</p>
                    </div>
                </body>
                </html>
            `);
        }

        // Mark user as verified and clear OTP
        console.log(`✅ OTP verification successful for user: ${user.email}`);
        console.log(`🔄 Before update - isVerified: ${user.isVerified}`);

        // Update user verification status and last login
        user.isVerified = true;           // ← THIS SETS VERIFIED TO TRUE
        user.otp = undefined;             // ← CLEARS THE OTP
        user.otpExpiry = undefined;       // ← CLEARS THE EXPIRY
        user.lastLoginDate = new Date();  // ← UPDATE LAST LOGIN TIME

        const savedUser = await user.save();                // ← SAVES TO DATABASE
        console.log(`💾 After save - isVerified: ${savedUser.isVerified}, User ID: ${savedUser._id}`);

        // Return JSON response instead of redirect for frontend handling
        return res.status(200).json({
            success: true,
            message: 'Email verified successfully',
            user: {
                id: savedUser._id,
                adminId: savedUser.adminId,
                username: savedUser.username,
                email: savedUser.email,
                isVerified: savedUser.isVerified,
                lastLoginDate: savedUser.lastLoginDate,
                ipAddress: savedUser.ipAddress,
                location: savedUser.location,
                company: savedUser.company,
                role: savedUser.role,
                isActive: savedUser.isActive
            }
        });

    } catch (error) {
        console.error("Error verifying OTP:", error);
        return res.status(500).send(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Verification Error</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f5f5f5; }
                    .container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
                    .error { color: #dc3545; }
                    .icon { font-size: 48px; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="icon">⚠️</div>
                    <h2 class="error">Verification Error</h2>
                    <p>An error occurred during verification. Please try again or contact support.</p>
                </div>
            </body>
            </html>
        `);
    }
};


// Reset/Setup MFA for admin user
const ResetAdminMFA = async (req, res) => {
    try {
        console.log('🔧 Resetting MFA for admin user...');

        // Find admin user
        let user = await Login.findOne({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Admin user not found'
            });
        }

        const speakeasy = require('speakeasy');
        const crypto = require('crypto');

        // Generate a master secret for MFA
        const secret = speakeasy.generateSecret({
            name: `CAM Transport (${user.email})`,
            issuer: 'CAM Transport',
            length: 32
        });

        // Set the master secret
        user.mfaSecret = secret.base32;

        // Create an active MFA device (for tracking only, no individual secret)
        const deviceId = crypto.randomBytes(16).toString('hex');
        const mfaDevice = {
            deviceId,
            deviceName: 'Admin Authenticator',
            isActive: true,
            registeredAt: new Date(),
            lastUsedAt: new Date(),
            deviceInfo: {
                userAgent: 'Admin Device',
                ipAddress: '127.0.0.1',
                platform: 'Admin'
            }
        };

        // Generate backup codes
        const backupCodes = [];
        for (let i = 0; i < 10; i++) {
            backupCodes.push({
                code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                used: false,
                usedAt: null
            });
        }

        // Enable MFA
        user.mfaEnabled = true;
        user.mfaSetupAt = new Date();
        user.mfaDevices = [mfaDevice];
        user.backupCodes = backupCodes;

        await user.save();

        console.log('✅ MFA reset completed for admin user');
        console.log('🔑 Secret for authenticator app:', secret.base32);

        return res.status(200).json({
            success: true,
            message: 'MFA reset successfully for admin user',
            data: {
                secret: secret.base32,
                qrCodeUrl: secret.otpauth_url,
                backupCodes: backupCodes.map(bc => bc.code)
            }
        });

    } catch (error) {
        console.error('Error resetting admin MFA:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Verify Email OTP for login (for users without MFA)
const VerifyEmailOTPLogin = async (req, res) => {
    try {
        const { userId, otp } = req.body;

        if (!userId || !otp) {
            return res.status(400).json({
                success: false,
                message: 'User ID and OTP are required'
            });
        }

        console.log('🔍 Email OTP login verification attempt:', { userId, otp });

        // Find the user by ID
        const user = await Login.findById(userId);

        if (!user) {
            console.log('❌ User not found for ID:', userId);
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log(`👤 Found user: ${user.email}`);
        console.log(`🔐 Stored OTP: ${user.otp}`);
        console.log(`⏰ OTP Expiry: ${user.otpExpiry}`);
        console.log(`🕐 Current Time: ${new Date()}`);

        // Check if OTP exists and is not expired
        if (!user.otp || !user.otpExpiry) {
            console.log('❌ No OTP found in database - user needs to request new login');
            return res.status(400).json({
                success: false,
                message: 'No OTP found. Please request a new login.'
            });
        }

        const currentTime = new Date();
        const isExpired = currentTime > user.otpExpiry;

        if (isExpired) {
            const expiredMinutesAgo = Math.floor((currentTime - user.otpExpiry) / (1000 * 60));
            console.log(`❌ OTP expired ${expiredMinutesAgo} minutes ago`);

            // Clear expired OTP
            user.otp = null;
            user.otpExpiry = null;
            await user.save();

            return res.status(400).json({
                success: false,
                message: 'OTP has expired. Please request a new login.'
            });
        }

        const timeLeftMinutes = Math.floor((user.otpExpiry - currentTime) / (1000 * 60));
        console.log(`⏳ OTP valid for ${timeLeftMinutes} more minutes`);

        // Verify OTP
        const providedOTP = parseInt(otp);
        const storedOTP = user.otp;

        console.log(`🔍 Comparing OTPs: provided=${providedOTP}, stored=${storedOTP}`);

        if (providedOTP !== storedOTP) {
            console.log('❌ OTP mismatch - invalid OTP provided');
            return res.status(400).json({
                success: false,
                message: 'Invalid OTP'
            });
        }

        console.log('✅ OTP verification successful!');

        // Clear OTP and update last login
        console.log('🧹 Clearing OTP after successful verification...');
        user.otp = null;
        user.otpExpiry = null;
        user.lastLoginDate = new Date();
        await user.save();

        console.log('✅ Email OTP login verification successful for user:', user.email);

        return res.status(200).json({
            success: true,
            message: 'Login successful',
            user: {
                id: user._id,
                _id: user._id,
                username: user.username,
                email: user.email,
                isVerified: user.isVerified,
                mfaEnabled: user.mfaEnabled,
                role: user.role,
                isActive: user.isActive
            }
        });

    } catch (error) {
        console.error('❌ Error verifying email OTP for login:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Utility function to clean up expired OTPs
const CleanupExpiredOTPs = async () => {
    try {
        const currentTime = new Date();
        const result = await Login.updateMany(
            {
                otpExpiry: { $lt: currentTime },
                otp: { $ne: null }
            },
            {
                $unset: { otp: "", otpExpiry: "" }
            }
        );

        if (result.modifiedCount > 0) {
            console.log(`🧹 Cleaned up ${result.modifiedCount} expired OTPs`);
        }

        return result;
    } catch (error) {
        console.error('❌ Error cleaning up expired OTPs:', error);
        return null;
    }
};

module.exports = {
    LoginAdmin,
    VerifyOTP,
    VerifyEmailOTPLogin,
    ResetAdminMFA,
    CleanupExpiredOTPs
};
