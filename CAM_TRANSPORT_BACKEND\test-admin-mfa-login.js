const axios = require('axios');
const speakeasy = require('speakeasy');

async function testAdminMFALogin() {
    console.log('🧪 Testing Admin MFA Login Flow');
    console.log('===============================\n');
    
    const fixedSecret = 'JBSWY3DPEHPK3PXP';
    
    try {
        // Step 1: Initial login (should require MFA)
        console.log('🔐 Step 1: Initial login attempt...');
        const loginResponse = await axios.post('http://localhost:8090/login', {
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin'
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Login response:', {
            status: loginResponse.status,
            requiresMFA: loginResponse.data.requiresMFA,
            message: loginResponse.data.message
        });
        
        if (!loginResponse.data.requiresMFA) {
            console.log('❌ Expected MFA requirement but got none');
            return;
        }
        
        // Step 2: Generate current TOTP token
        console.log('\n🔐 Step 2: Generating TOTP token...');
        const currentToken = speakeasy.totp({
            secret: fixedSecret,
            encoding: 'base32'
        });
        
        console.log(`Generated TOTP: ${currentToken}`);
        console.log(`Secret used: ${fixedSecret}`);
        
        // Step 3: Verify MFA token
        console.log('\n🔐 Step 3: Verifying MFA token...');
        const mfaResponse = await axios.post('http://localhost:8090/login/mfa-verify', {
            username: 'admin',
            email: '<EMAIL>',
            password: 'verified',
            mfaToken: currentToken,
            step: 'mfa'
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ MFA verification response:', {
            status: mfaResponse.status,
            success: mfaResponse.data.success,
            message: mfaResponse.data.message
        });
        
        if (mfaResponse.data.success) {
            console.log('\n🎉 SUCCESS! Admin MFA login is working correctly!');
            console.log('✅ You can now use your authenticator app with the fixed secret');
            console.log('✅ Current working token:', currentToken);
        } else {
            console.log('\n❌ MFA verification failed:', mfaResponse.data.message);
        }
        
    } catch (error) {
        console.error('❌ Error during MFA login test:', {
            status: error.response?.status,
            message: error.response?.data?.message || error.message,
            data: error.response?.data
        });
    }
}

if (require.main === module) {
    testAdminMFALogin();
}
