module.exports = {

"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/libs/auth.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Third-party Imports
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/prisma-adapter/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
;
// No fake data imports - using real backend only
const prisma = new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(prisma),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'Credentials',
            type: 'credentials',
            credentials: {},
            async authorize (credentials) {
                console.log('🚀 AUTHORIZE FUNCTION CALLED!', credentials);
                if (!credentials) {
                    return null;
                }
                const { userId, username, email, password, mfaToken, step } = credentials;
                console.log('🔐 Auth attempt:', {
                    email,
                    username,
                    step,
                    hasMfaToken: !!mfaToken,
                    userId
                });
                // Handle email-verified users (after OTP verification)
                if (step === 'email-verified') {
                    console.log('📧 Email-verified user login');
                    try {
                        const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8090") || 'http://localhost:8090';
                        const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        if (response.ok) {
                            const result = await response.json();
                            const apiUser = result.user;
                            if (apiUser && apiUser.isVerified) {
                                return {
                                    id: apiUser.id || apiUser._id,
                                    username: apiUser.username,
                                    name: apiUser.username,
                                    email: apiUser.email,
                                    image: '/images/avatars/1.png',
                                    role: apiUser.role || 'admin',
                                    isVerified: true,
                                    requiresMFA: false,
                                    mfaEnabled: apiUser.mfaEnabled || false,
                                    mfaVerified: true // Consider verified for email-verified users
                                };
                            }
                        }
                    } catch (error) {
                        console.error('❌ Error fetching verified user:', error);
                    }
                    return null;
                }
                // No fake user lookup - only use real backend data
                console.log('👤 Using real backend authentication only');
                // Step 1: Password verification
                if (step !== 'mfa') {
                    console.log('🔐 Regular login step');
                    try {
                        const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8090") || 'http://localhost:8090';
                        console.log('🌐 Making login API call to:', `${API_BASE_URL}/login`);
                        const response = await fetch(`${API_BASE_URL}/login`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: username,
                                email: email,
                                password: password
                            })
                        });
                        console.log('📡 Login API response status:', response.status);
                        if (response.ok) {
                            const result = await response.json();
                            console.log('✅ Login API success:', result);
                            if (result.success && result.user) {
                                const apiUser = result.user;
                                console.log('✅ Backend user data:', {
                                    id: apiUser.id || apiUser._id,
                                    username: apiUser.username,
                                    email: apiUser.email,
                                    isVerified: apiUser.isVerified,
                                    mfaEnabled: apiUser.mfaEnabled,
                                    requiresMFA: result.requiresMFA,
                                    requiresEmailOTP: result.requiresEmailOTP,
                                    requiresEmailVerification: result.requiresEmailVerification
                                });
                                // CRITICAL: Check if backend requires Email OTP verification
                                if (result.requiresEmailOTP) {
                                    console.log('📧 Backend requires Email OTP verification for login - BLOCKING session creation');
                                    console.log('🚫 Returning null to prevent session creation until OTP is verified');
                                    // SECURITY: Do NOT return a user object when OTP verification is required
                                    // This prevents session creation and forces the frontend to handle OTP verification
                                    // The frontend Login.jsx component will detect requiresEmailOTP and show OTP input
                                    return null;
                                }
                                // Check if backend requires Email verification (for new users)
                                if (result.requiresEmailVerification) {
                                    console.log('📧 Backend requires Email verification for new user');
                                    return {
                                        id: apiUser.id || apiUser._id,
                                        username: apiUser.username,
                                        name: apiUser.username,
                                        email: apiUser.email,
                                        image: '/images/avatars/1.png',
                                        role: apiUser.role || 'super_admin',
                                        isVerified: false,
                                        requiresEmailVerification: true,
                                        mfaEnabled: apiUser.mfaEnabled,
                                        mfaVerified: false,
                                        // Special flag to indicate verification is pending
                                        verificationPending: true,
                                        verificationType: 'email'
                                    };
                                }
                                // Check if backend requires MFA verification
                                if (result.requiresMFA) {
                                    console.log('🔐 Backend requires MFA verification');
                                    return {
                                        id: apiUser.id || apiUser._id,
                                        username: apiUser.username,
                                        name: apiUser.username,
                                        email: apiUser.email,
                                        image: '/images/avatars/1.png',
                                        role: apiUser.role || 'super_admin',
                                        isVerified: apiUser.isVerified,
                                        requiresMFA: true,
                                        mfaEnabled: apiUser.mfaEnabled,
                                        mfaVerified: false
                                    };
                                }
                                // If no additional verification required, login successful
                                console.log('✅ Login successful, no additional verification required');
                                return {
                                    id: apiUser.id || apiUser._id,
                                    username: apiUser.username,
                                    name: apiUser.username,
                                    email: apiUser.email,
                                    image: '/images/avatars/1.png',
                                    role: apiUser.role || 'super_admin',
                                    isVerified: apiUser.isVerified,
                                    requiresMFA: false,
                                    mfaEnabled: apiUser.mfaEnabled,
                                    mfaVerified: true
                                };
                            }
                        } else {
                            const errorData = await response.json();
                            console.log('❌ Login API error:', errorData);
                        }
                    } catch (error) {
                        console.error('❌ Login API request failed:', error);
                    }
                    // No fallback - only use real backend data
                    console.log('❌ Backend API failed, no fallback allowed');
                    return null;
                }
                // Step 2: MFA verification
                if (step === 'mfa' && mfaToken) {
                    console.log('🔐 MFA verification step - using backend API only');
                    if (username && email) {
                        console.log('🔐 MFA verification step triggered:', {
                            step,
                            username,
                            email,
                            mfaToken: mfaToken ? `${mfaToken.substring(0, 2)}****` : 'null',
                            tokenLength: mfaToken ? mfaToken.length : 0
                        });
                        try {
                            const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8090") || 'http://localhost:8090';
                            console.log('🌐 Making MFA verification API call to:', `${API_BASE_URL}/login/mfa-verify`);
                            const requestBody = {
                                username: username,
                                email: email,
                                password: 'verified',
                                mfaToken: mfaToken,
                                step: 'mfa'
                            };
                            console.log('📤 Sending MFA verification request:', requestBody);
                            const response = await fetch(`${API_BASE_URL}/login/mfa-verify`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(requestBody)
                            });
                            console.log('📡 MFA verification API response status:', response.status);
                            console.log('📡 MFA verification API response headers:', Object.fromEntries(response.headers.entries()));
                            if (response.ok) {
                                let result;
                                try {
                                    const responseText = await response.text();
                                    console.log('📥 Raw MFA verification response:', responseText);
                                    result = JSON.parse(responseText);
                                } catch (parseError) {
                                    console.error('❌ Failed to parse MFA verification response:', parseError);
                                    return null;
                                }
                                console.log('✅ MFA verification API response:', result);
                                if (result && result.success) {
                                    console.log('✅ MFA verification successful, returning user');
                                    // Use the real user data from backend response, not fake data
                                    const backendUser = result.user;
                                    const updatedUser = {
                                        id: backendUser.id || backendUser._id,
                                        username: backendUser.username,
                                        name: backendUser.username,
                                        email: backendUser.email,
                                        image: '/images/avatars/1.png',
                                        role: backendUser.role || 'super_admin',
                                        isVerified: backendUser.isVerified,
                                        requiresMFA: backendUser.mfaEnabled || false,
                                        mfaEnabled: backendUser.mfaEnabled || false,
                                        mfaVerified: true,
                                        mfaVerifiedAt: Date.now() // Add timestamp
                                    };
                                    console.log('🔄 Returning updated user with real backend data:', updatedUser);
                                    return updatedUser;
                                } else {
                                    console.log('❌ MFA verification failed:', result?.message || 'Unknown error');
                                    return null;
                                }
                            } else {
                                console.log('❌ MFA verification API call failed with status:', response.status);
                                const errorText = await response.text();
                                console.log('❌ Error response:', errorText);
                                return null;
                            }
                        } catch (error) {
                            console.error('❌ Error verifying MFA:', error);
                            // Don't throw here, return null to let NextAuth handle the error
                            return null;
                        }
                    } else {
                        console.log('❌ MFA step called but missing user data:', {
                            step,
                            hasMfaToken: !!mfaToken,
                            username,
                            email
                        });
                        return null;
                    }
                }
                console.log('❌ No valid authentication path found');
                return null;
            }
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60 // 30 days
    },
    pages: {
        signIn: '/login'
    },
    callbacks: {
        async jwt ({ token, user, trigger, session }) {
            // Handle new login (when user object is provided)
            if (user) {
                // CRITICAL SECURITY FIX: Always reset MFA state for new logins
                token.id = user.id;
                token.name = user.name;
                token.email = user.email;
                token.role = user.role || 'admin';
                token.isVerified = user.isVerified;
                token.requiresMFA = user.requiresMFA;
                token.loginTimestamp = user.loginTimestamp || Date.now();
                // ALWAYS reset MFA verification for new login attempts
                token.mfaVerified = false;
                token.mfaVerifiedAt = null;
                // CRITICAL: Preserve OTP and verification flags
                token.otpPending = user.otpPending || false;
                token.otpType = user.otpType || null;
                token.verificationPending = user.verificationPending || false;
                token.verificationType = user.verificationType || null;
                token.requiresEmailOTP = user.requiresEmailOTP || false;
                token.requiresEmailVerification = user.requiresEmailVerification || false;
                console.log('🔐 JWT: New login detected for user:', user.email, {
                    otpPending: token.otpPending,
                    otpType: token.otpType,
                    verificationPending: token.verificationPending,
                    verificationType: token.verificationType,
                    requiresEmailOTP: token.requiresEmailOTP,
                    requiresEmailVerification: token.requiresEmailVerification
                });
            }
            // For MFA verification updates during the same session
            if (trigger === 'update' && session?.mfaVerified !== undefined) {
                token.mfaVerified = session.mfaVerified;
                if (session.mfaVerified) {
                    token.mfaVerifiedAt = Date.now();
                }
                console.log('🔐 JWT: MFA verification updated via session update', {
                    mfaVerified: token.mfaVerified,
                    mfaVerifiedAt: token.mfaVerifiedAt
                });
            }
            return token;
        },
        async session ({ session, token }) {
            if (session.user) {
                session.user.id = token.id;
                session.user.name = token.name;
                session.user.email = token.email;
                session.user.role = token.role;
                session.user.isVerified = token.isVerified;
                session.user.requiresMFA = token.requiresMFA;
                session.user.mfaVerified = token.mfaVerified;
                session.user.mfaVerifiedAt = token.mfaVerifiedAt;
                session.user.loginTimestamp = token.loginTimestamp;
                // CRITICAL: Include OTP and verification flags in session
                session.user.otpPending = token.otpPending;
                session.user.otpType = token.otpType;
                session.user.verificationPending = token.verificationPending;
                session.user.verificationType = token.verificationType;
                session.user.requiresEmailOTP = token.requiresEmailOTP;
                session.user.requiresEmailVerification = token.requiresEmailVerification;
                console.log('📋 Session created with flags:', {
                    email: session.user.email,
                    otpPending: session.user.otpPending,
                    otpType: session.user.otpType,
                    verificationPending: session.user.verificationPending,
                    verificationType: session.user.verificationType
                });
            }
            return session;
        }
    }
};
}}),
"[project]/src/app/api/auth/[...nextauth]/route.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Third-party Imports
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
// Lib Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$libs$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/libs/auth.js [app-route] (ecmascript)");
;
;
/*
 * As we do not have backend server, the refresh token feature has not been incorporated into the template.
 * Please refer https://next-auth.js.org/tutorials/refresh-token-rotation link for a reference.
 */ const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$libs$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__be8ba32f._.js.map