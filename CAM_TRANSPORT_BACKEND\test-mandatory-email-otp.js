const axios = require('axios');
const mongoose = require('mongoose');
const Login = require('./model/Login');

// Connect to MongoDB using the same configuration as the main app
async function connectDB() {
    try {
        // Use local database for testing to avoid conflicts
        await mongoose.connect('mongodb://localhost:27017/cam_transport_test', {
            // Remove deprecated options
        });
        console.log('✅ Connected to MongoDB (test database)');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        console.error('💡 Make sure MongoDB is running locally or update the connection string');
        process.exit(1);
    }
}

// Create a test user with mfaEnabled: false
async function createTestUser() {
    const testUser = {
        username: 'testuser_' + Date.now(),
        email: 'testuser_' + Date.now() + '@example.com',
        password: 'testpassword123',
        adminId: 'test_' + Date.now(),
        isVerified: false, // Newly created user
        mfaEnabled: false, // No MFA enabled
        role: 'admin',
        isActive: true
    };

    try {
        // Check if user already exists
        const existingUser = await Login.findOne({ email: testUser.email });
        if (existingUser) {
            console.log('🔄 User already exists, using existing user');
            return existingUser;
        }

        // Create new user
        const user = new Login(testUser);
        await user.save();
        
        console.log('✅ Test user created successfully:');
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Password: ${testUser.password}`);
        console.log(`   isVerified: ${user.isVerified}`);
        console.log(`   mfaEnabled: ${user.mfaEnabled}`);
        
        return { ...user.toObject(), plainPassword: testUser.password };
    } catch (error) {
        console.error('❌ Failed to create test user:', error);
        throw error;
    }
}

// Test the mandatory email OTP flow
async function testMandatoryEmailOTP(user) {
    console.log('\n🧪 Testing Mandatory Email OTP Flow...');
    console.log('=' .repeat(60));
    
    const credentials = {
        username: user.username,
        email: user.email,
        password: user.plainPassword
    };
    
    console.log('📋 Test Scenario: Newly created user login');
    console.log(`   Username: ${credentials.username}`);
    console.log(`   Email: ${credentials.email}`);
    console.log(`   isVerified: ${user.isVerified}`);
    console.log(`   mfaEnabled: ${user.mfaEnabled}`);
    console.log('\n🎯 Expected Result: MUST require email OTP verification');
    
    try {
        console.log('\n🔐 Step 1: Attempting login...');
        const loginResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n📡 Login API Response:');
        console.log('Status:', loginResponse.status);
        console.log('Response Data:', JSON.stringify(loginResponse.data, null, 2));
        
        // Analyze the response
        if (loginResponse.data.requiresEmailOTP) {
            console.log('\n🎉 SUCCESS: Mandatory Email OTP is working correctly!');
            console.log('✅ Login correctly requires email OTP verification');
            console.log('✅ User cannot proceed without OTP verification');
            console.log('📧 OTP should have been sent to:', credentials.email);
            console.log('👤 User ID for verification:', loginResponse.data.user.id);
            
            return {
                success: true,
                requiresEmailOTP: true,
                userId: loginResponse.data.user.id,
                email: credentials.email,
                testPassed: true
            };
            
        } else if (loginResponse.data.requiresMFA) {
            console.log('\n❌ UNEXPECTED: User requires MFA instead of email OTP');
            console.log('⚠️ This should not happen for users with mfaEnabled: false');
            return {
                success: true,
                requiresMFA: true,
                testPassed: false,
                issue: 'User requires MFA instead of email OTP'
            };
            
        } else {
            console.log('\n🚨 CRITICAL SECURITY ISSUE: Login completed without verification!');
            console.log('❌ User was able to login without any secondary verification');
            console.log('❌ This violates the mandatory email OTP requirement');
            return {
                success: true,
                noAdditionalVerification: true,
                testPassed: false,
                issue: 'Login completed without any verification - SECURITY BREACH'
            };
        }
        
    } catch (error) {
        console.log('\n❌ Login Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
            
            // Check if this is expected behavior
            if (error.response.status === 500 && error.response.data.message?.includes('email OTP is mandatory')) {
                console.log('\n✅ GOOD: Server correctly blocked login due to email OTP requirement');
                return {
                    success: false,
                    testPassed: true,
                    issue: 'Server correctly blocked login - email OTP mandatory'
                };
            }
        } else {
            console.log('Network Error:', error.message);
        }
        
        return {
            success: false,
            testPassed: false,
            error: error.response?.data || error.message
        };
    }
}

// Test OTP verification
async function testOTPVerification(userId, otp) {
    console.log('\n🧪 Testing OTP Verification...');
    console.log(`User ID: ${userId}`);
    console.log(`OTP: ${otp}`);
    
    try {
        const response = await axios.post('http://localhost:8090/login/verify-email-otp', {
            userId: userId,
            otp: parseInt(otp)
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ OTP Verification Success!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        return { success: true, verified: true };
        
    } catch (error) {
        console.log('❌ OTP Verification Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error:', JSON.stringify(error.response.data, null, 2));
        }
        return { success: false, error: error.response?.data || error.message };
    }
}

// Cleanup test user
async function cleanupTestUser(user) {
    try {
        await Login.findByIdAndDelete(user._id);
        console.log('🧹 Test user cleaned up successfully');
    } catch (error) {
        console.error('⚠️ Failed to cleanup test user:', error);
    }
}

// Main test function
async function runMandatoryEmailOTPTest() {
    console.log('🚀 Starting Mandatory Email OTP Test Suite');
    console.log('=' .repeat(60));
    
    try {
        // Connect to database
        await connectDB();
        
        // Create test user
        console.log('\n📝 Creating test user...');
        const testUser = await createTestUser();
        
        // Test the mandatory email OTP flow
        const testResult = await testMandatoryEmailOTP(testUser);
        
        // Print final results
        console.log('\n' + '=' .repeat(60));
        console.log('📊 TEST RESULTS:');
        console.log('=' .repeat(60));
        
        if (testResult.testPassed) {
            console.log('🎉 TEST PASSED: Mandatory Email OTP is working correctly!');
            console.log('✅ Security requirement satisfied');
            console.log('✅ Users without MFA cannot bypass email OTP verification');
        } else {
            console.log('❌ TEST FAILED: Mandatory Email OTP is NOT working correctly!');
            console.log('🚨 SECURITY ISSUE DETECTED');
            console.log('❌ Issue:', testResult.issue || 'Unknown issue');
        }
        
        // Cleanup
        console.log('\n🧹 Cleaning up...');
        await cleanupTestUser(testUser);
        
        // Close database connection
        await mongoose.connection.close();
        console.log('✅ Database connection closed');
        
        return testResult.testPassed;
        
    } catch (error) {
        console.error('❌ Test suite failed:', error);
        await mongoose.connection.close();
        return false;
    }
}

// Export functions for use in other scripts
module.exports = {
    runMandatoryEmailOTPTest,
    testMandatoryEmailOTP,
    testOTPVerification,
    createTestUser,
    cleanupTestUser
};

// Run the test if this script is executed directly
if (require.main === module) {
    runMandatoryEmailOTPTest().then(passed => {
        process.exit(passed ? 0 : 1);
    }).catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}
