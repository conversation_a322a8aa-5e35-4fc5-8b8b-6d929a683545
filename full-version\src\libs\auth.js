// Third-party Imports
import Cred<PERSON>Provider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { PrismaClient } from '@prisma/client'

// No fake data imports - using real backend only

const prisma = new PrismaClient()

export const authOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialProvider({
      name: 'Credentials',
      type: 'credentials',
      credentials: {},
      async authorize(credentials) {
        console.log('🚀 AUTHORIZE FUNCTION CALLED!', credentials)

        if (!credentials) {
          return null
        }

        const { userId, username, email, password, mfaToken, step } = credentials

        console.log('🔐 Auth attempt:', { email, username, step, hasMfaToken: !!mfaToken, userId })

        // Handle email-verified users (after OTP verification)
        if (step === 'email-verified') {
          console.log('📧 Email-verified user login')

          try {
            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
            const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              }
            })

            if (response.ok) {
              const result = await response.json()
              const apiUser = result.user

              if (apiUser && apiUser.isVerified) {
                return {
                  id: apiUser.id || apiUser._id,
                  username: apiUser.username,
                  name: apiUser.username,
                  email: apiUser.email,
                  image: '/images/avatars/1.png',
                  role: apiUser.role || 'admin',
                  isVerified: true,
                  requiresMFA: false, // Skip MFA for now, will be set up later
                  mfaEnabled: apiUser.mfaEnabled || false,
                  mfaVerified: true // Consider verified for email-verified users
                }
              }
            }
          } catch (error) {
            console.error('❌ Error fetching verified user:', error)
          }

          return null
        }

        // No fake user lookup - only use real backend data
        console.log('👤 Using real backend authentication only')

        // Step 1: Password verification
        if (step !== 'mfa') {
          console.log('🔐 Regular login step')

          try {
            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

            console.log('🌐 Making login API call to:', `${API_BASE_URL}/login`)

            const response = await fetch(`${API_BASE_URL}/login`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                username: username,
                email: email,
                password: password
              })
            })

            console.log('📡 Login API response status:', response.status)

            if (response.ok) {
              const result = await response.json()

              console.log('✅ Login API success:', result)

              if (result.success && result.user) {
                const apiUser = result.user

                console.log('✅ Backend user data:', {
                  id: apiUser.id || apiUser._id,
                  username: apiUser.username,
                  email: apiUser.email,
                  isVerified: apiUser.isVerified,
                  mfaEnabled: apiUser.mfaEnabled,
                  requiresMFA: result.requiresMFA,
                  requiresEmailOTP: result.requiresEmailOTP,
                  requiresEmailVerification: result.requiresEmailVerification
                });

                // CRITICAL: Check if backend requires Email OTP verification
                if (result.requiresEmailOTP) {
                  console.log('📧 Backend requires Email OTP verification for login - BLOCKING session creation')
                  console.log('🚫 Returning null to prevent session creation until OTP is verified')

                  // SECURITY: Do NOT return a user object when OTP verification is required
                  // This prevents session creation and forces the frontend to handle OTP verification
                  // The frontend Login.jsx component will detect requiresEmailOTP and show OTP input
                  return null
                }

                // Check if backend requires Email verification (for new users)
                if (result.requiresEmailVerification) {
                  console.log('📧 Backend requires Email verification for new user')

                  return {
                    id: apiUser.id || apiUser._id,
                    username: apiUser.username,
                    name: apiUser.username,
                    email: apiUser.email,
                    image: '/images/avatars/1.png',
                    role: apiUser.role || 'super_admin',
                    isVerified: false,
                    requiresEmailVerification: true,
                    mfaEnabled: apiUser.mfaEnabled,
                    mfaVerified: false,
                    // Special flag to indicate verification is pending
                    verificationPending: true,
                    verificationType: 'email'
                  }
                }

                // Check if backend requires MFA verification
                if (result.requiresMFA) {
                  console.log('🔐 Backend requires MFA verification')

                  return {
                    id: apiUser.id || apiUser._id,
                    username: apiUser.username,
                    name: apiUser.username,
                    email: apiUser.email,
                    image: '/images/avatars/1.png',
                    role: apiUser.role || 'super_admin',
                    isVerified: apiUser.isVerified,
                    requiresMFA: true,
                    mfaEnabled: apiUser.mfaEnabled,
                    mfaVerified: false
                  }
                }

                // If no additional verification required, login successful
                console.log('✅ Login successful, no additional verification required')

                return {
                  id: apiUser.id || apiUser._id,
                  username: apiUser.username,
                  name: apiUser.username,
                  email: apiUser.email,
                  image: '/images/avatars/1.png',
                  role: apiUser.role || 'super_admin',
                  isVerified: apiUser.isVerified,
                  requiresMFA: false,
                  mfaEnabled: apiUser.mfaEnabled,
                  mfaVerified: true
                }
              }
            } else {
              const errorData = await response.json()

              console.log('❌ Login API error:', errorData)
            }
          } catch (error) {
            console.error('❌ Login API request failed:', error)
          }

          // No fallback - only use real backend data
          console.log('❌ Backend API failed, no fallback allowed')
          return null
        }

        // Step 2: MFA verification
        if (step === 'mfa' && mfaToken) {
          console.log('🔐 MFA verification step - using backend API only')

          if (username && email) {
            console.log('🔐 MFA verification step triggered:', {
              step,
              username,
              email,
              mfaToken: mfaToken ? `${mfaToken.substring(0, 2)}****` : 'null',
              tokenLength: mfaToken ? mfaToken.length : 0
            })

            try {
              const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

              console.log('🌐 Making MFA verification API call to:', `${API_BASE_URL}/login/mfa-verify`)

              const requestBody = {
                username: username,
                email: email,
                password: 'verified',
                mfaToken: mfaToken,
                step: 'mfa'
              }

              console.log('📤 Sending MFA verification request:', requestBody)

              const response = await fetch(`${API_BASE_URL}/login/mfa-verify`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
              })

              console.log('📡 MFA verification API response status:', response.status)
              console.log('📡 MFA verification API response headers:', Object.fromEntries(response.headers.entries()))

              if (response.ok) {
                let result
                try {
                  const responseText = await response.text()
                  console.log('📥 Raw MFA verification response:', responseText)
                  result = JSON.parse(responseText)
                } catch (parseError) {
                  console.error('❌ Failed to parse MFA verification response:', parseError)
                  return null
                }

                console.log('✅ MFA verification API response:', result)

                if (result && result.success) {
                  console.log('✅ MFA verification successful, returning user')

                  // Use the real user data from backend response, not fake data
                  const backendUser = result.user

                  const updatedUser = {
                    id: backendUser.id || backendUser._id, // Use real ObjectId from backend
                    username: backendUser.username,
                    name: backendUser.username,
                    email: backendUser.email,
                    image: '/images/avatars/1.png',
                    role: backendUser.role || 'super_admin', // Temporary: default to super_admin for testing
                    isVerified: backendUser.isVerified,
                    requiresMFA: backendUser.mfaEnabled || false,
                    mfaEnabled: backendUser.mfaEnabled || false,
                    mfaVerified: true, // Only mark as verified for this session
                    mfaVerifiedAt: Date.now() // Add timestamp
                  }

                  console.log('🔄 Returning updated user with real backend data:', updatedUser)

                  return updatedUser
                } else {
                  console.log('❌ MFA verification failed:', result?.message || 'Unknown error')
                  return null
                }
              } else {
                console.log('❌ MFA verification API call failed with status:', response.status)
                const errorText = await response.text()
                console.log('❌ Error response:', errorText)
                return null
              }
            } catch (error) {
              console.error('❌ Error verifying MFA:', error)
              // Don't throw here, return null to let NextAuth handle the error
              return null
            }
          } else {
            console.log('❌ MFA step called but missing user data:', {
              step,
              hasMfaToken: !!mfaToken,
              username,
              email
            })
            return null
          }
        }

        console.log('❌ No valid authentication path found')
        return null
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60 // 30 days
  },
  pages: {
    signIn: '/login'
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Handle new login (when user object is provided)
      if (user) {
        // CRITICAL SECURITY FIX: Always reset MFA state for new logins
        token.id = user.id
        token.name = user.name
        token.email = user.email
        token.role = user.role || 'admin'
        token.isVerified = user.isVerified
        token.requiresMFA = user.requiresMFA
        token.loginTimestamp = user.loginTimestamp || Date.now()

        // Set MFA verification status based on user type
        // Email-verified users are considered verified, others need MFA verification
        token.mfaVerified = user.mfaVerified || false
        token.mfaVerifiedAt = user.mfaVerified ? Date.now() : null

        // CRITICAL: Preserve OTP and verification flags
        token.otpPending = user.otpPending || false
        token.otpType = user.otpType || null
        token.verificationPending = user.verificationPending || false
        token.verificationType = user.verificationType || null
        token.requiresEmailOTP = user.requiresEmailOTP || false
        token.requiresEmailVerification = user.requiresEmailVerification || false

        console.log('🔐 JWT: New login detected for user:', user.email, {
          otpPending: token.otpPending,
          otpType: token.otpType,
          verificationPending: token.verificationPending,
          verificationType: token.verificationType,
          requiresEmailOTP: token.requiresEmailOTP,
          requiresEmailVerification: token.requiresEmailVerification
        })
      }

      // For MFA verification updates during the same session
      if (trigger === 'update' && session?.mfaVerified !== undefined) {
        token.mfaVerified = session.mfaVerified
        if (session.mfaVerified) {
          token.mfaVerifiedAt = Date.now()
        }
        console.log('🔐 JWT: MFA verification updated via session update', {
          mfaVerified: token.mfaVerified,
          mfaVerifiedAt: token.mfaVerifiedAt
        })
      }

      return token
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id
        session.user.name = token.name
        session.user.email = token.email
        session.user.role = token.role
        session.user.isVerified = token.isVerified
        session.user.requiresMFA = token.requiresMFA
        session.user.mfaVerified = token.mfaVerified
        session.user.mfaVerifiedAt = token.mfaVerifiedAt
        session.user.loginTimestamp = token.loginTimestamp

        // CRITICAL: Include OTP and verification flags in session
        session.user.otpPending = token.otpPending
        session.user.otpType = token.otpType
        session.user.verificationPending = token.verificationPending
        session.user.verificationType = token.verificationType
        session.user.requiresEmailOTP = token.requiresEmailOTP
        session.user.requiresEmailVerification = token.requiresEmailVerification

        console.log('📋 Session created with flags:', {
          email: session.user.email,
          otpPending: session.user.otpPending,
          otpType: session.user.otpType,
          verificationPending: session.user.verificationPending,
          verificationType: session.user.verificationType
        })
      }

      return session
    }
  }
}
