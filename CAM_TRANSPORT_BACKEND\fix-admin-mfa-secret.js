const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function fixAdminMFASecret() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');
        
        // Find the admin user
        const user = await Login.findOne({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });
        
        if (!user) {
            console.log('❌ Admin user not found');
            return;
        }
        
        console.log('👤 Found Admin User:');
        console.log(`   ID: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Current MFA Secret: ${user.mfaSecret}`);
        
        // Set the fixed secret that matches the authenticator app
        const fixedSecret = 'JBSWY3DPEHPK3PXP';
        
        console.log('\n🔧 Updating MFA secret to fixed value...');
        console.log(`   New Secret: ${fixedSecret}`);
        
        // Update the MFA secret
        user.mfaSecret = fixedSecret;
        await user.save();
        
        console.log('✅ MFA secret updated successfully!');
        
        // Test token generation with the new secret
        const speakeasy = require('speakeasy');
        const currentToken = speakeasy.totp({
            secret: fixedSecret,
            encoding: 'base32'
        });
        
        console.log('\n🔐 Testing with new secret:');
        console.log(`   Current TOTP: ${currentToken}`);
        console.log(`   Secret: ${fixedSecret}`);
        
        // Test verification
        const verified = speakeasy.totp.verify({
            secret: fixedSecret,
            encoding: 'base32',
            token: currentToken,
            window: 1
        });
        
        console.log(`   Verification test: ${verified ? 'PASS' : 'FAIL'}`);
        
        console.log('\n🎯 INSTRUCTIONS:');
        console.log('================');
        console.log('1. Remove the old entry from your authenticator app');
        console.log('2. Add a new entry with this secret: JBSWY3DPEHPK3PXP');
        console.log('3. Or scan this QR code setup URL:');
        console.log(`   otpauth://totp/CAM%20Transport%20(<EMAIL>)?secret=${fixedSecret}&issuer=CAM%20Transport`);
        console.log('4. Use the 6-digit code from your authenticator app to login');
        console.log(`5. Current expected code: ${currentToken}`);
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

if (require.main === module) {
    fixAdminMFASecret();
}
