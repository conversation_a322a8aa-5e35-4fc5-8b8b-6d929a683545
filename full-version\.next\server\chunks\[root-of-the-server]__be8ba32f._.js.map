{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/libs/auth.js"], "sourcesContent": ["// Third-party Imports\r\nimport Cred<PERSON>Provider from 'next-auth/providers/credentials'\r\nimport GoogleProvider from 'next-auth/providers/google'\r\nimport { PrismaAdapter } from '@auth/prisma-adapter'\r\nimport { PrismaClient } from '@prisma/client'\r\n\r\n// No fake data imports - using real backend only\r\n\r\nconst prisma = new PrismaClient()\r\n\r\nexport const authOptions = {\r\n  adapter: PrismaAdapter(prisma),\r\n  providers: [\r\n    CredentialProvider({\r\n      name: 'Credentials',\r\n      type: 'credentials',\r\n      credentials: {},\r\n      async authorize(credentials) {\r\n        console.log('🚀 AUTHORIZE FUNCTION CALLED!', credentials)\r\n\r\n        if (!credentials) {\r\n          return null\r\n        }\r\n\r\n        const { userId, username, email, password, mfaToken, step } = credentials\r\n\r\n        console.log('🔐 Auth attempt:', { email, username, step, hasMfaToken: !!mfaToken, userId })\r\n\r\n        // Handle email-verified users (after OTP verification)\r\n        if (step === 'email-verified') {\r\n          console.log('📧 Email-verified user login')\r\n\r\n          try {\r\n            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n            const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {\r\n              method: 'GET',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              }\r\n            })\r\n\r\n            if (response.ok) {\r\n              const result = await response.json()\r\n              const apiUser = result.user\r\n\r\n              if (apiUser && apiUser.isVerified) {\r\n                return {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  name: apiUser.username,\r\n                  email: apiUser.email,\r\n                  image: '/images/avatars/1.png',\r\n                  role: apiUser.role || 'admin',\r\n                  isVerified: true,\r\n                  requiresMFA: false, // Skip MFA for now, will be set up later\r\n                  mfaEnabled: apiUser.mfaEnabled || false,\r\n                  mfaVerified: true // Consider verified for email-verified users\r\n                }\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Error fetching verified user:', error)\r\n          }\r\n\r\n          return null\r\n        }\r\n\r\n        // No fake user lookup - only use real backend data\r\n        console.log('👤 Using real backend authentication only')\r\n\r\n        // Step 1: Password verification\r\n        if (step !== 'mfa') {\r\n          console.log('🔐 Regular login step')\r\n\r\n          try {\r\n            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n            console.log('🌐 Making login API call to:', `${API_BASE_URL}/login`)\r\n\r\n            const response = await fetch(`${API_BASE_URL}/login`, {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                username: username,\r\n                email: email,\r\n                password: password\r\n              })\r\n            })\r\n\r\n            console.log('📡 Login API response status:', response.status)\r\n\r\n            if (response.ok) {\r\n              const result = await response.json()\r\n\r\n              console.log('✅ Login API success:', result)\r\n\r\n              if (result.success && result.user) {\r\n                const apiUser = result.user\r\n\r\n                console.log('✅ Backend user data:', {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  email: apiUser.email,\r\n                  isVerified: apiUser.isVerified,\r\n                  mfaEnabled: apiUser.mfaEnabled,\r\n                  requiresMFA: result.requiresMFA,\r\n                  requiresEmailOTP: result.requiresEmailOTP,\r\n                  requiresEmailVerification: result.requiresEmailVerification\r\n                });\r\n\r\n                // CRITICAL: Check if backend requires Email OTP verification\r\n                if (result.requiresEmailOTP) {\r\n                  console.log('📧 Backend requires Email OTP verification for login - BLOCKING session creation')\r\n                  console.log('🚫 Returning null to prevent session creation until OTP is verified')\r\n\r\n                  // SECURITY: Do NOT return a user object when OTP verification is required\r\n                  // This prevents session creation and forces the frontend to handle OTP verification\r\n                  // The frontend Login.jsx component will detect requiresEmailOTP and show OTP input\r\n                  return null\r\n                }\r\n\r\n                // Check if backend requires Email verification (for new users)\r\n                if (result.requiresEmailVerification) {\r\n                  console.log('📧 Backend requires Email verification for new user')\r\n\r\n                  return {\r\n                    id: apiUser.id || apiUser._id,\r\n                    username: apiUser.username,\r\n                    name: apiUser.username,\r\n                    email: apiUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: apiUser.role || 'super_admin',\r\n                    isVerified: false,\r\n                    requiresEmailVerification: true,\r\n                    mfaEnabled: apiUser.mfaEnabled,\r\n                    mfaVerified: false,\r\n                    // Special flag to indicate verification is pending\r\n                    verificationPending: true,\r\n                    verificationType: 'email'\r\n                  }\r\n                }\r\n\r\n                // Check if backend requires MFA verification\r\n                if (result.requiresMFA) {\r\n                  console.log('🔐 Backend requires MFA verification')\r\n\r\n                  return {\r\n                    id: apiUser.id || apiUser._id,\r\n                    username: apiUser.username,\r\n                    name: apiUser.username,\r\n                    email: apiUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: apiUser.role || 'super_admin',\r\n                    isVerified: apiUser.isVerified,\r\n                    requiresMFA: true,\r\n                    mfaEnabled: apiUser.mfaEnabled,\r\n                    mfaVerified: false\r\n                  }\r\n                }\r\n\r\n                // If no additional verification required, login successful\r\n                console.log('✅ Login successful, no additional verification required')\r\n\r\n                return {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  name: apiUser.username,\r\n                  email: apiUser.email,\r\n                  image: '/images/avatars/1.png',\r\n                  role: apiUser.role || 'super_admin',\r\n                  isVerified: apiUser.isVerified,\r\n                  requiresMFA: false,\r\n                  mfaEnabled: apiUser.mfaEnabled,\r\n                  mfaVerified: true\r\n                }\r\n              }\r\n            } else {\r\n              const errorData = await response.json()\r\n\r\n              console.log('❌ Login API error:', errorData)\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Login API request failed:', error)\r\n          }\r\n\r\n          // No fallback - only use real backend data\r\n          console.log('❌ Backend API failed, no fallback allowed')\r\n          return null\r\n        }\r\n\r\n        // Step 2: MFA verification\r\n        if (step === 'mfa' && mfaToken) {\r\n          console.log('🔐 MFA verification step - using backend API only')\r\n\r\n          if (username && email) {\r\n            console.log('🔐 MFA verification step triggered:', {\r\n              step,\r\n              username,\r\n              email,\r\n              mfaToken: mfaToken ? `${mfaToken.substring(0, 2)}****` : 'null',\r\n              tokenLength: mfaToken ? mfaToken.length : 0\r\n            })\r\n\r\n            try {\r\n              const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n              console.log('🌐 Making MFA verification API call to:', `${API_BASE_URL}/login/mfa-verify`)\r\n\r\n              const requestBody = {\r\n                username: username,\r\n                email: email,\r\n                password: 'verified',\r\n                mfaToken: mfaToken,\r\n                step: 'mfa'\r\n              }\r\n\r\n              console.log('📤 Sending MFA verification request:', requestBody)\r\n\r\n              const response = await fetch(`${API_BASE_URL}/login/mfa-verify`, {\r\n                method: 'POST',\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify(requestBody)\r\n              })\r\n\r\n              console.log('📡 MFA verification API response status:', response.status)\r\n              console.log('📡 MFA verification API response headers:', Object.fromEntries(response.headers.entries()))\r\n\r\n              if (response.ok) {\r\n                let result\r\n                try {\r\n                  const responseText = await response.text()\r\n                  console.log('📥 Raw MFA verification response:', responseText)\r\n                  result = JSON.parse(responseText)\r\n                } catch (parseError) {\r\n                  console.error('❌ Failed to parse MFA verification response:', parseError)\r\n                  return null\r\n                }\r\n\r\n                console.log('✅ MFA verification API response:', result)\r\n\r\n                if (result && result.success) {\r\n                  console.log('✅ MFA verification successful, returning user')\r\n\r\n                  // Use the real user data from backend response, not fake data\r\n                  const backendUser = result.user\r\n\r\n                  const updatedUser = {\r\n                    id: backendUser.id || backendUser._id, // Use real ObjectId from backend\r\n                    username: backendUser.username,\r\n                    name: backendUser.username,\r\n                    email: backendUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: backendUser.role || 'super_admin', // Temporary: default to super_admin for testing\r\n                    isVerified: backendUser.isVerified,\r\n                    requiresMFA: backendUser.mfaEnabled || false,\r\n                    mfaEnabled: backendUser.mfaEnabled || false,\r\n                    mfaVerified: true, // Only mark as verified for this session\r\n                    mfaVerifiedAt: Date.now() // Add timestamp\r\n                  }\r\n\r\n                  console.log('🔄 Returning updated user with real backend data:', updatedUser)\r\n\r\n                  return updatedUser\r\n                } else {\r\n                  console.log('❌ MFA verification failed:', result?.message || 'Unknown error')\r\n                  return null\r\n                }\r\n              } else {\r\n                console.log('❌ MFA verification API call failed with status:', response.status)\r\n                const errorText = await response.text()\r\n                console.log('❌ Error response:', errorText)\r\n                return null\r\n              }\r\n            } catch (error) {\r\n              console.error('❌ Error verifying MFA:', error)\r\n              // Don't throw here, return null to let NextAuth handle the error\r\n              return null\r\n            }\r\n          } else {\r\n            console.log('❌ MFA step called but missing user data:', {\r\n              step,\r\n              hasMfaToken: !!mfaToken,\r\n              username,\r\n              email\r\n            })\r\n            return null\r\n          }\r\n        }\r\n\r\n        console.log('❌ No valid authentication path found')\r\n        return null\r\n      }\r\n    }),\r\n    GoogleProvider({\r\n      clientId: process.env.GOOGLE_CLIENT_ID,\r\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 30 * 24 * 60 * 60 // 30 days\r\n  },\r\n  pages: {\r\n    signIn: '/login'\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user, trigger, session }) {\r\n      // Handle new login (when user object is provided)\r\n      if (user) {\r\n        // CRITICAL SECURITY FIX: Always reset MFA state for new logins\r\n        token.id = user.id\r\n        token.name = user.name\r\n        token.email = user.email\r\n        token.role = user.role || 'admin'\r\n        token.isVerified = user.isVerified\r\n        token.requiresMFA = user.requiresMFA\r\n        token.loginTimestamp = user.loginTimestamp || Date.now()\r\n\r\n        // ALWAYS reset MFA verification for new login attempts\r\n        token.mfaVerified = false\r\n        token.mfaVerifiedAt = null\r\n\r\n        // CRITICAL: Preserve OTP and verification flags\r\n        token.otpPending = user.otpPending || false\r\n        token.otpType = user.otpType || null\r\n        token.verificationPending = user.verificationPending || false\r\n        token.verificationType = user.verificationType || null\r\n        token.requiresEmailOTP = user.requiresEmailOTP || false\r\n        token.requiresEmailVerification = user.requiresEmailVerification || false\r\n\r\n        console.log('🔐 JWT: New login detected for user:', user.email, {\r\n          otpPending: token.otpPending,\r\n          otpType: token.otpType,\r\n          verificationPending: token.verificationPending,\r\n          verificationType: token.verificationType,\r\n          requiresEmailOTP: token.requiresEmailOTP,\r\n          requiresEmailVerification: token.requiresEmailVerification\r\n        })\r\n      }\r\n\r\n      // For MFA verification updates during the same session\r\n      if (trigger === 'update' && session?.mfaVerified !== undefined) {\r\n        token.mfaVerified = session.mfaVerified\r\n        if (session.mfaVerified) {\r\n          token.mfaVerifiedAt = Date.now()\r\n        }\r\n        console.log('🔐 JWT: MFA verification updated via session update', {\r\n          mfaVerified: token.mfaVerified,\r\n          mfaVerifiedAt: token.mfaVerifiedAt\r\n        })\r\n      }\r\n\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (session.user) {\r\n        session.user.id = token.id\r\n        session.user.name = token.name\r\n        session.user.email = token.email\r\n        session.user.role = token.role\r\n        session.user.isVerified = token.isVerified\r\n        session.user.requiresMFA = token.requiresMFA\r\n        session.user.mfaVerified = token.mfaVerified\r\n        session.user.mfaVerifiedAt = token.mfaVerifiedAt\r\n        session.user.loginTimestamp = token.loginTimestamp\r\n\r\n        // CRITICAL: Include OTP and verification flags in session\r\n        session.user.otpPending = token.otpPending\r\n        session.user.otpType = token.otpType\r\n        session.user.verificationPending = token.verificationPending\r\n        session.user.verificationType = token.verificationType\r\n        session.user.requiresEmailOTP = token.requiresEmailOTP\r\n        session.user.requiresEmailVerification = token.requiresEmailVerification\r\n\r\n        console.log('📋 Session created with flags:', {\r\n          email: session.user.email,\r\n          otpPending: session.user.otpPending,\r\n          otpType: session.user.otpType,\r\n          verificationPending: session.user.verificationPending,\r\n          verificationType: session.user.verificationType\r\n        })\r\n      }\r\n\r\n      return session\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;AACA;AACA;;;;;AAEA,iDAAiD;AAEjD,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY;AAExB,MAAM,cAAc;IACzB,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE;IACvB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAkB,AAAD,EAAE;YACjB,MAAM;YACN,MAAM;YACN,aAAa,CAAC;YACd,MAAM,WAAU,WAAW;gBACzB,QAAQ,GAAG,CAAC,iCAAiC;gBAE7C,IAAI,CAAC,aAAa;oBAChB,OAAO;gBACT;gBAEA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG;gBAE9D,QAAQ,GAAG,CAAC,oBAAoB;oBAAE;oBAAO;oBAAU;oBAAM,aAAa,CAAC,CAAC;oBAAU;gBAAO;gBAEzF,uDAAuD;gBACvD,IAAI,SAAS,kBAAkB;oBAC7B,QAAQ,GAAG,CAAC;oBAEZ,IAAI;wBACF,MAAM,eAAe,6DAAmC;wBACxD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,QAAQ,EAAE;4BACrE,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,SAAS,MAAM,SAAS,IAAI;4BAClC,MAAM,UAAU,OAAO,IAAI;4BAE3B,IAAI,WAAW,QAAQ,UAAU,EAAE;gCACjC,OAAO;oCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,MAAM,QAAQ,QAAQ;oCACtB,OAAO,QAAQ,KAAK;oCACpB,OAAO;oCACP,MAAM,QAAQ,IAAI,IAAI;oCACtB,YAAY;oCACZ,aAAa;oCACb,YAAY,QAAQ,UAAU,IAAI;oCAClC,aAAa,KAAK,6CAA6C;gCACjE;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;oBAEA,OAAO;gBACT;gBAEA,mDAAmD;gBACnD,QAAQ,GAAG,CAAC;gBAEZ,gCAAgC;gBAChC,IAAI,SAAS,OAAO;oBAClB,QAAQ,GAAG,CAAC;oBAEZ,IAAI;wBACF,MAAM,eAAe,6DAAmC;wBAExD,QAAQ,GAAG,CAAC,gCAAgC,GAAG,aAAa,MAAM,CAAC;wBAEnE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;4BACpD,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;gCACnB,UAAU;gCACV,OAAO;gCACP,UAAU;4BACZ;wBACF;wBAEA,QAAQ,GAAG,CAAC,iCAAiC,SAAS,MAAM;wBAE5D,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,SAAS,MAAM,SAAS,IAAI;4BAElC,QAAQ,GAAG,CAAC,wBAAwB;4BAEpC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gCACjC,MAAM,UAAU,OAAO,IAAI;gCAE3B,QAAQ,GAAG,CAAC,wBAAwB;oCAClC,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,OAAO,QAAQ,KAAK;oCACpB,YAAY,QAAQ,UAAU;oCAC9B,YAAY,QAAQ,UAAU;oCAC9B,aAAa,OAAO,WAAW;oCAC/B,kBAAkB,OAAO,gBAAgB;oCACzC,2BAA2B,OAAO,yBAAyB;gCAC7D;gCAEA,6DAA6D;gCAC7D,IAAI,OAAO,gBAAgB,EAAE;oCAC3B,QAAQ,GAAG,CAAC;oCACZ,QAAQ,GAAG,CAAC;oCAEZ,0EAA0E;oCAC1E,oFAAoF;oCACpF,mFAAmF;oCACnF,OAAO;gCACT;gCAEA,+DAA+D;gCAC/D,IAAI,OAAO,yBAAyB,EAAE;oCACpC,QAAQ,GAAG,CAAC;oCAEZ,OAAO;wCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;wCAC7B,UAAU,QAAQ,QAAQ;wCAC1B,MAAM,QAAQ,QAAQ;wCACtB,OAAO,QAAQ,KAAK;wCACpB,OAAO;wCACP,MAAM,QAAQ,IAAI,IAAI;wCACtB,YAAY;wCACZ,2BAA2B;wCAC3B,YAAY,QAAQ,UAAU;wCAC9B,aAAa;wCACb,mDAAmD;wCACnD,qBAAqB;wCACrB,kBAAkB;oCACpB;gCACF;gCAEA,6CAA6C;gCAC7C,IAAI,OAAO,WAAW,EAAE;oCACtB,QAAQ,GAAG,CAAC;oCAEZ,OAAO;wCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;wCAC7B,UAAU,QAAQ,QAAQ;wCAC1B,MAAM,QAAQ,QAAQ;wCACtB,OAAO,QAAQ,KAAK;wCACpB,OAAO;wCACP,MAAM,QAAQ,IAAI,IAAI;wCACtB,YAAY,QAAQ,UAAU;wCAC9B,aAAa;wCACb,YAAY,QAAQ,UAAU;wCAC9B,aAAa;oCACf;gCACF;gCAEA,2DAA2D;gCAC3D,QAAQ,GAAG,CAAC;gCAEZ,OAAO;oCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,MAAM,QAAQ,QAAQ;oCACtB,OAAO,QAAQ,KAAK;oCACpB,OAAO;oCACP,MAAM,QAAQ,IAAI,IAAI;oCACtB,YAAY,QAAQ,UAAU;oCAC9B,aAAa;oCACb,YAAY,QAAQ,UAAU;oCAC9B,aAAa;gCACf;4BACF;wBACF,OAAO;4BACL,MAAM,YAAY,MAAM,SAAS,IAAI;4BAErC,QAAQ,GAAG,CAAC,sBAAsB;wBACpC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;oBAEA,2CAA2C;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,SAAS,SAAS,UAAU;oBAC9B,QAAQ,GAAG,CAAC;oBAEZ,IAAI,YAAY,OAAO;wBACrB,QAAQ,GAAG,CAAC,uCAAuC;4BACjD;4BACA;4BACA;4BACA,UAAU,WAAW,GAAG,SAAS,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;4BACzD,aAAa,WAAW,SAAS,MAAM,GAAG;wBAC5C;wBAEA,IAAI;4BACF,MAAM,eAAe,6DAAmC;4BAExD,QAAQ,GAAG,CAAC,2CAA2C,GAAG,aAAa,iBAAiB,CAAC;4BAEzF,MAAM,cAAc;gCAClB,UAAU;gCACV,OAAO;gCACP,UAAU;gCACV,UAAU;gCACV,MAAM;4BACR;4BAEA,QAAQ,GAAG,CAAC,wCAAwC;4BAEpD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;gCAC/D,QAAQ;gCACR,SAAS;oCACP,gBAAgB;gCAClB;gCACA,MAAM,KAAK,SAAS,CAAC;4BACvB;4BAEA,QAAQ,GAAG,CAAC,4CAA4C,SAAS,MAAM;4BACvE,QAAQ,GAAG,CAAC,6CAA6C,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;4BAEpG,IAAI,SAAS,EAAE,EAAE;gCACf,IAAI;gCACJ,IAAI;oCACF,MAAM,eAAe,MAAM,SAAS,IAAI;oCACxC,QAAQ,GAAG,CAAC,qCAAqC;oCACjD,SAAS,KAAK,KAAK,CAAC;gCACtB,EAAE,OAAO,YAAY;oCACnB,QAAQ,KAAK,CAAC,gDAAgD;oCAC9D,OAAO;gCACT;gCAEA,QAAQ,GAAG,CAAC,oCAAoC;gCAEhD,IAAI,UAAU,OAAO,OAAO,EAAE;oCAC5B,QAAQ,GAAG,CAAC;oCAEZ,8DAA8D;oCAC9D,MAAM,cAAc,OAAO,IAAI;oCAE/B,MAAM,cAAc;wCAClB,IAAI,YAAY,EAAE,IAAI,YAAY,GAAG;wCACrC,UAAU,YAAY,QAAQ;wCAC9B,MAAM,YAAY,QAAQ;wCAC1B,OAAO,YAAY,KAAK;wCACxB,OAAO;wCACP,MAAM,YAAY,IAAI,IAAI;wCAC1B,YAAY,YAAY,UAAU;wCAClC,aAAa,YAAY,UAAU,IAAI;wCACvC,YAAY,YAAY,UAAU,IAAI;wCACtC,aAAa;wCACb,eAAe,KAAK,GAAG,GAAG,gBAAgB;oCAC5C;oCAEA,QAAQ,GAAG,CAAC,qDAAqD;oCAEjE,OAAO;gCACT,OAAO;oCACL,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,WAAW;oCAC7D,OAAO;gCACT;4BACF,OAAO;gCACL,QAAQ,GAAG,CAAC,mDAAmD,SAAS,MAAM;gCAC9E,MAAM,YAAY,MAAM,SAAS,IAAI;gCACrC,QAAQ,GAAG,CAAC,qBAAqB;gCACjC,OAAO;4BACT;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,iEAAiE;4BACjE,OAAO;wBACT;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC,4CAA4C;4BACtD;4BACA,aAAa,CAAC,CAAC;4BACf;4BACA;wBACF;wBACA,OAAO;oBACT;gBACF;gBAEA,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;QACF;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,GAAG,UAAU;IACtC;IACA,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,kDAAkD;YAClD,IAAI,MAAM;gBACR,+DAA+D;gBAC/D,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI;gBAC1B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,cAAc,GAAG,KAAK,cAAc,IAAI,KAAK,GAAG;gBAEtD,uDAAuD;gBACvD,MAAM,WAAW,GAAG;gBACpB,MAAM,aAAa,GAAG;gBAEtB,gDAAgD;gBAChD,MAAM,UAAU,GAAG,KAAK,UAAU,IAAI;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO,IAAI;gBAChC,MAAM,mBAAmB,GAAG,KAAK,mBAAmB,IAAI;gBACxD,MAAM,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;gBAClD,MAAM,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;gBAClD,MAAM,yBAAyB,GAAG,KAAK,yBAAyB,IAAI;gBAEpE,QAAQ,GAAG,CAAC,wCAAwC,KAAK,KAAK,EAAE;oBAC9D,YAAY,MAAM,UAAU;oBAC5B,SAAS,MAAM,OAAO;oBACtB,qBAAqB,MAAM,mBAAmB;oBAC9C,kBAAkB,MAAM,gBAAgB;oBACxC,kBAAkB,MAAM,gBAAgB;oBACxC,2BAA2B,MAAM,yBAAyB;gBAC5D;YACF;YAEA,uDAAuD;YACvD,IAAI,YAAY,YAAY,SAAS,gBAAgB,WAAW;gBAC9D,MAAM,WAAW,GAAG,QAAQ,WAAW;gBACvC,IAAI,QAAQ,WAAW,EAAE;oBACvB,MAAM,aAAa,GAAG,KAAK,GAAG;gBAChC;gBACA,QAAQ,GAAG,CAAC,uDAAuD;oBACjE,aAAa,MAAM,WAAW;oBAC9B,eAAe,MAAM,aAAa;gBACpC;YACF;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;gBAChD,QAAQ,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc;gBAElD,0DAA0D;gBAC1D,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;gBACpC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,MAAM,mBAAmB;gBAC5D,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,gBAAgB;gBACtD,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,gBAAgB;gBACtD,QAAQ,IAAI,CAAC,yBAAyB,GAAG,MAAM,yBAAyB;gBAExE,QAAQ,GAAG,CAAC,kCAAkC;oBAC5C,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,YAAY,QAAQ,IAAI,CAAC,UAAU;oBACnC,SAAS,QAAQ,IAAI,CAAC,OAAO;oBAC7B,qBAAqB,QAAQ,IAAI,CAAC,mBAAmB;oBACrD,kBAAkB,QAAQ,IAAI,CAAC,gBAAgB;gBACjD;YACF;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/app/api/auth/%5B...nextauth%5D/route.js"], "sourcesContent": ["// Third-party Imports\r\nimport NextAuth from 'next-auth'\r\n\r\n// Lib Imports\r\nimport { authOptions } from '@/libs/auth'\r\n\r\n/*\r\n * As we do not have backend server, the refresh token feature has not been incorporated into the template.\r\n * Please refer https://next-auth.js.org/tutorials/refresh-token-rotation link for a reference.\r\n */\r\nconst handler = NextAuth(authOptions)\r\n\r\nexport { handler as GET, handler as POST }\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AAEA,cAAc;AACd;;;AAEA;;;CAGC,GACD,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,qHAAA,CAAA,cAAW", "debugId": null}}]}