const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function disableAdminMFA() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');
        
        // Find the admin user
        const user = await Login.findOne({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });
        
        if (!user) {
            console.log('❌ Admin user not found');
            return;
        }
        
        console.log('👤 Found Admin User:');
        console.log(`   ID: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Current MFA Enabled: ${user.mfaEnabled}`);
        
        // Disable MFA completely
        console.log('\n🔧 Disabling MFA for admin account...');
        user.mfaEnabled = false;
        user.mfaSecret = null;
        user.mfaDevices = [];
        user.backupCodes = [];
        user.mfaSetupAt = null;
        
        await user.save();
        
        console.log('✅ MFA DISABLED SUCCESSFULLY for admin account!');
        console.log('\n🎯 VERIFICATION:');
        console.log(`   MFA Enabled: ${user.mfaEnabled}`);
        console.log(`   MFA Secret: ${user.mfaSecret || 'null'}`);
        console.log(`   MFA Devices: ${user.mfaDevices.length} devices`);
        console.log(`   Backup Codes: ${user.backupCodes.length} codes`);
        
        console.log('\n✅ ADMIN ACCOUNT STATUS:');
        console.log('   - MFA is now DISABLED');
        console.log('   - Admin can login directly without email OTP (special privilege)');
        console.log('   - Other users without MFA will still require email OTP');
        console.log('\n🧪 TEST LOGIN:');
        console.log('   Username: admin');
        console.log('   Email: <EMAIL>');
        console.log('   Password: admin');
        console.log('   Expected: Direct login without MFA or email OTP');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n✅ Database connection closed');
    }
}

if (require.main === module) {
    disableAdminMFA();
}
