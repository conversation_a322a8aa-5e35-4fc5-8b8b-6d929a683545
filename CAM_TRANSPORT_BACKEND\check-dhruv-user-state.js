const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function checkDhruvUserState() {
    try {
        // Connect to the database using the same config as the main app
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');
        
        // Find the dhruv user
        const user = await Login.findOne({
            $or: [
                { username: 'dhruv' },
                { email: '<EMAIL>' }
            ]
        });
        
        if (!user) {
            console.log('❌ Dhruv user not found');
            return;
        }
        
        console.log('👤 Dhruv User State:');
        console.log('=' .repeat(50));
        console.log(`ID: ${user._id}`);
        console.log(`Username: ${user.username}`);
        console.log(`Email: ${user.email}`);
        console.log(`isVerified: ${user.isVerified}`);
        console.log(`mfaEnabled: ${user.mfaEnabled}`);
        console.log(`isActive: ${user.isActive}`);
        console.log(`Role: ${user.role}`);
        console.log(`Last Login: ${user.lastLoginDate}`);
        console.log(`Created: ${user.createdAt}`);
        console.log(`Updated: ${user.updatedAt}`);
        
        if (user.otp) {
            console.log(`Current OTP: ${user.otp}`);
            console.log(`OTP Expiry: ${user.otpExpiry}`);
            console.log(`OTP Expired: ${new Date() > user.otpExpiry}`);
        } else {
            console.log('No active OTP');
        }
        
        if (user.mfaEnabled) {
            console.log(`MFA Secret: ${user.mfaSecret ? 'Present' : 'Missing'}`);
            console.log(`MFA Devices: ${user.mfaDevices ? user.mfaDevices.length : 0}`);
            console.log(`Backup Codes: ${user.backupCodes ? user.backupCodes.length : 0}`);
        }
        
        console.log('\n🔍 Expected Behavior Analysis:');
        console.log('=' .repeat(50));
        
        if (!user.mfaEnabled) {
            console.log('✅ User has mfaEnabled=false');
            console.log('✅ Should require email OTP verification');
            console.log('✅ Should NOT be able to login without OTP');
        } else {
            console.log('🔐 User has mfaEnabled=true');
            console.log('🔐 Should require TOTP verification');
        }
        
        if (!user.isActive) {
            console.log('⚠️ User is INACTIVE - login should be blocked');
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('✅ Database connection closed');
    }
}

if (require.main === module) {
    checkDhruvUserState();
}
