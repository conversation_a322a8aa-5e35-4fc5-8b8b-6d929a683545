const { Router } = require('express');
const Login = require('../model/Login');
const bcrypt = require('bcrypt');
const { sendEmail } = require('../service/Mailer');
const { requireSuperAdmin, requireAdmin } = require('../middleware/roleAuth');
const {
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    changeUserPassword
} = require('../controller/UserManagement');
const { generateEmailOTP, verifyEmailOTP } = require('../controller/EmailOTP');


const UserProfileRouter = Router();



// Check if any users exist in the database
UserProfileRouter.get('/check-users', async (req, res) => {
    try {
        console.log('🔍 Checking if any users exist in database...');

        const userCount = await Login.countDocuments();
        const hasUsers = userCount > 0;

        console.log(`✅ User count: ${userCount}, hasUsers: ${hasUsers}`);

        return res.status(200).json({
            success: true,
            hasUsers: hasUsers,
            userCount: userCount
        });

    } catch (error) {
        console.error('❌ Error checking user existence:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get all admin accounts (for admin management)
UserProfileRouter.get('/admins/list', async (req, res) => {
    try {
        console.log('🔍 Fetching all admin accounts...');

        const admins = await Login.find({}).select('-password -otp -otpExpiry').sort({ createdAt: -1 });

        console.log(`✅ Found ${admins.length} admin accounts`);

        return res.status(200).json({
            success: true,
            admins: admins.map(admin => ({
                id: admin._id,
                adminId: admin.adminId,
                username: admin.username,
                email: admin.email,
                isVerified: admin.isVerified,
                lastLoginDate: admin.lastLoginDate,
                ipAddress: admin.ipAddress,
                location: admin.location,
                company: admin.company,
                role: admin.role,
                isActive: admin.isActive,
                createdAt: admin.createdAt
            })),
            totalAdmins: admins.length
        });

    } catch (error) {
        console.error('❌ Error fetching admin accounts:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});















// ==================== USER MANAGEMENT APIS (SUPER ADMIN ONLY) ====================
// NOTE: These specific routes MUST come BEFORE the generic /:userId routes

// Get all users with pagination and filtering
UserProfileRouter.get('/users', requireSuperAdmin, getAllUsers);

// Create a new user
UserProfileRouter.post('/users/create', requireSuperAdmin, createUser);

// Update an existing user
UserProfileRouter.put('/users/:userId', requireSuperAdmin, updateUser);

// Delete a user
UserProfileRouter.delete('/users/:userId', requireSuperAdmin, deleteUser);

// Change user password
UserProfileRouter.patch('/users/:userId/change-password', requireSuperAdmin, changeUserPassword);

// Toggle user status (activate/deactivate)
UserProfileRouter.patch('/users/:userId/toggle-status', requireSuperAdmin, async (req, res) => {
    try {
        const { userId: targetUserId } = req.params;

        const user = await Login.findById(targetUserId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Prevent super admin from being deactivated by non-super admin
        if (user.role === 'super_admin' && req.user.role !== 'super_admin') {
            return res.status(403).json({
                success: false,
                message: 'Only super admin can modify super admin accounts'
            });
        }

        // Prevent user from deactivating themselves
        if (user._id.toString() === req.user._id.toString()) {
            return res.status(403).json({
                success: false,
                message: 'Cannot deactivate your own account'
            });
        }

        user.isActive = !user.isActive;
        await user.save();

        console.log(`✅ User ${user.isActive ? 'activated' : 'deactivated'}: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`,
            user: {
                id: user._id,
                email: user.email,
                isActive: user.isActive
            }
        });

    } catch (error) {
        console.error('❌ Error toggling user status:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get user statistics
UserProfileRouter.get('/users/stats', requireSuperAdmin, async (req, res) => {
    try {
        const totalUsers = await Login.countDocuments();
        const activeUsers = await Login.countDocuments({ isActive: true });
        const inactiveUsers = await Login.countDocuments({ isActive: false });
        const superAdmins = await Login.countDocuments({ role: 'super_admin' });
        const admins = await Login.countDocuments({ role: 'admin' });
        const verifiedUsers = await Login.countDocuments({ isVerified: true });
        const mfaEnabledUsers = await Login.countDocuments({ mfaEnabled: true });

        return res.status(200).json({
            success: true,
            stats: {
                totalUsers,
                activeUsers,
                inactiveUsers,
                superAdmins,
                admins,
                verifiedUsers,
                mfaEnabledUsers
            }
        });

    } catch (error) {
        console.error('❌ Error fetching user statistics:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// ==================== INDIVIDUAL USER PROFILE APIS ====================
// NOTE: These generic routes MUST come AFTER the specific routes above

// Get user profile by ID
UserProfileRouter.get('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;

        console.log('🔍 Fetching user profile for ID:', userId);

        const user = await Login.findById(userId).select('-password -otp -otpExpiry');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('✅ User profile found:', user.email);

        return res.status(200).json({
            success: true,
            user: {
                id: user._id,
                adminId: user.adminId,
                username: user.username,
                email: user.email,
                isVerified: user.isVerified,
                lastLoginDate: user.lastLoginDate,
                ipAddress: user.ipAddress,
                location: user.location,
                company: user.company,
                role: user.role,
                isActive: user.isActive,
                mfaEnabled: user.mfaEnabled, // Include MFA status for NextAuth
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        });

    } catch (error) {
        console.error('❌ Error fetching user profile:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Update user profile
UserProfileRouter.put('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const { username, company } = req.body;

        console.log('🔄 Updating user profile for ID:', userId);

        const user = await Login.findById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Update allowed fields
        if (username) user.username = username.trim();
        if (company) user.company = company.trim();

        const updatedUser = await user.save();

        console.log('✅ User profile updated:', updatedUser.email);

        return res.status(200).json({
            success: true,
            message: 'Profile updated successfully',
            user: {
                id: updatedUser._id,
                username: updatedUser.username,
                email: updatedUser.email,
                isVerified: updatedUser.isVerified,
                lastLoginDate: updatedUser.lastLoginDate,
                ipAddress: updatedUser.ipAddress,
                location: updatedUser.location,
                company: updatedUser.company,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt
            }
        });

    } catch (error) {
        console.error('❌ Error updating user profile:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// ==================== EMAIL OTP VERIFICATION FOR NEW USERS ====================

// Generate email OTP for new user verification
UserProfileRouter.post('/email-otp/generate', generateEmailOTP);

// Verify email OTP
UserProfileRouter.post('/email-otp/verify', verifyEmailOTP);

module.exports = UserProfileRouter;
